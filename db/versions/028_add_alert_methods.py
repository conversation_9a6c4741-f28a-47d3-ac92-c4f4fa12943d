from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

alert_methods = Table(
    'alert_methods', meta,
    Column('id', Integer, primary_key=True, autoincrement=True),
    Column('name', String(255), nullable=False, comment='告警方式名称'),
    Column('method_type', String(50), nullable=False, comment='告警方式类型: email, dingtalk, webhook, sms等'),
    Column('description', String(500), comment='告警方式描述'),
    
    # 通用字段
    Column('is_enabled', Integer, default=1, comment='是否启用: 1启用, 0禁用'),
    Column('is_default', Integer, default=0, comment='是否为默认告警方式: 1是, 0否'),
    Column('order_id', Integer, nullable=False, default=0, comment='排序ID'),
    Column('status', String(50), default='active', comment='状态'),
    
    # 邮件告警字段
    Column('smtp_from', String(255), comment='SMTP发件人邮箱'),
    Column('smtp_smarthost', String(255), comment='SMTP服务器地址:端口'),
    Column('smtp_auth_username', String(255), comment='SMTP认证用户名'),
    Column('smtp_auth_password', String(255), comment='SMTP认证密码'),
    Column('smtp_require_tls', Integer, default=1, comment='是否需要TLS: 1需要, 0不需要'),
    Column('email_to', String(1000), comment='邮件接收人，多个用逗号分隔'),
    Column('email_subject', String(500), comment='邮件主题模板'),
    Column('email_body', String(2000), comment='邮件内容模板'),
    
    # 钉钉告警字段
    Column('dingtalk_webhook_url', String(500), comment='钉钉机器人Webhook地址'),
    Column('dingtalk_secret', String(255), comment='钉钉机器人加签密钥'),
    Column('dingtalk_at_mobiles', String(500), comment='钉钉@手机号，多个用逗号分隔'),
    Column('dingtalk_at_all', Integer, default=0, comment='是否@所有人: 1是, 0否'),
    Column('dingtalk_title', String(500), comment='钉钉消息标题模板'),
    Column('dingtalk_content', String(2000), comment='钉钉消息内容模板'),
    
    # Webhook告警字段
    Column('webhook_url', String(500), comment='Webhook地址'),
    Column('webhook_method', String(10), default='POST', comment='HTTP方法: POST, PUT等'),
    Column('webhook_headers', String(1000), comment='HTTP请求头，JSON格式'),
    Column('webhook_body', String(2000), comment='HTTP请求体模板'),
    Column('webhook_timeout', Integer, default=10, comment='超时时间(秒)'),
    
    # 短信告警字段
    Column('sms_api_url', String(500), comment='短信API地址'),
    Column('sms_api_key', String(255), comment='短信API密钥'),
    Column('sms_api_secret', String(255), comment='短信API密钥'),
    Column('sms_template_id', String(100), comment='短信模板ID'),
    Column('sms_sign_name', String(100), comment='短信签名'),
    Column('sms_phone_numbers', String(500), comment='短信接收号码，多个用逗号分隔'),
    Column('sms_content', String(500), comment='短信内容模板'),
    
    # 企业微信告警字段
    Column('wechat_webhook_url', String(500), comment='企业微信机器人Webhook地址'),
    Column('wechat_mentioned_list', String(500), comment='企业微信@用户列表，多个用逗号分隔'),
    Column('wechat_mentioned_mobile_list', String(500), comment='企业微信@手机号列表，多个用逗号分隔'),
    Column('wechat_title', String(500), comment='企业微信消息标题模板'),
    Column('wechat_content', String(2000), comment='企业微信消息内容模板'),
    
    # 时间字段
    Column('created_at', DateTime(timezone=True), server_default=func.now()),
    Column('updated_at', DateTime(timezone=True), server_default=func.now(), onupdate=func.now()),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    alert_methods.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    alert_methods.drop()
